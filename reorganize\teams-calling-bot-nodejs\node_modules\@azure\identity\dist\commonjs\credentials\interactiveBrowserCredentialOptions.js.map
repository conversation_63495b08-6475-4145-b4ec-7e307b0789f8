{"version": 3, "file": "interactiveBrowserCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/interactiveBrowserCredentialOptions.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { BrowserCustomizationOptions } from \"./browserCustomizationOptions.js\";\nimport type { BrokerAuthOptions } from \"./brokerAuthOptions.js\";\nimport type { CredentialPersistenceOptions } from \"./credentialPersistenceOptions.js\";\nimport type { InteractiveCredentialOptions } from \"./interactiveCredentialOptions.js\";\n\n/**\n * (Browser-only feature)\n * The \"login style\" to use in the authentication flow:\n * - \"redirect\" redirects the user to the authentication page and then\n *   redirects them back to the page once authentication is completed.\n * - \"popup\" opens a new browser window through with the redirect flow\n *   is initiated.  The user's existing browser window does not leave\n *   the current page\n */\nexport type BrowserLoginStyle = \"redirect\" | \"popup\";\n\n/**\n * Defines the common options for the InteractiveBrowserCredential class.\n */\nexport interface InteractiveBrowserCredentialNodeOptions\n  extends InteractiveCredentialOptions,\n    CredentialPersistenceOptions,\n    BrowserCustomizationOptions,\n    BrokerAuthOptions {\n  /**\n   * Gets the redirect URI of the application. This should be same as the value\n   * in the application registration portal.  Defaults to `window.location.href`.\n   * This field is no longer required for Node.js.\n   */\n  redirectUri?: string | (() => string);\n\n  /**\n   * The Microsoft Entra tenant (directory) ID.\n   */\n  tenantId?: string;\n\n  /**\n   * The Client ID of the Microsoft Entra application that users will sign into.\n   * It is recommended that developers register their applications and assign appropriate roles.\n   * For more information, visit https://aka.ms/identity/AppRegistrationAndRoleAssignment.\n   * If not specified, users will authenticate to an Azure development application,\n   * which is not recommended for production scenarios.\n   */\n  clientId?: string;\n\n  /**\n   * loginHint allows a user name to be pre-selected for interactive logins.\n   * Setting this option skips the account selection prompt and immediately attempts to login with the specified account.\n   */\n  loginHint?: string;\n}\n\n/**\n * Defines the common options for the InteractiveBrowserCredential class.\n */\nexport interface InteractiveBrowserCredentialInBrowserOptions extends InteractiveCredentialOptions {\n  /**\n   * Gets the redirect URI of the application. This should be same as the value\n   * in the application registration portal.  Defaults to `window.location.href`.\n   * This field is no longer required for Node.js.\n   */\n  redirectUri?: string | (() => string);\n\n  /**\n   * The Microsoft Entra tenant (directory) ID.\n   */\n  tenantId?: string;\n\n  /**\n   * The Client ID of the Microsoft Entra application that users will sign into.\n   * This parameter is required on the browser.\n   * Developers need to register their applications and assign appropriate roles.\n   * For more information, visit https://aka.ms/identity/AppRegistrationAndRoleAssignment.\n   */\n  clientId: string;\n\n  /**\n   * Specifies whether a redirect or a popup window should be used to\n   * initiate the user authentication flow. Possible values are \"redirect\"\n   * or \"popup\" (default) for browser and \"popup\" (default) for node.\n   *\n   */\n  loginStyle?: BrowserLoginStyle;\n\n  /**\n   * loginHint allows a user name to be pre-selected for interactive logins.\n   * Setting this option skips the account selection prompt and immediately attempts to login with the specified account.\n   */\n  loginHint?: string;\n}\n"]}