{"version": 3, "file": "imdsRetryPolicy.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/imdsRetryPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAmBlC,0CAqBC;AArCD,kEAAwD;AAGxD,gDAAuD;AAEvD,0EAA0E;AAC1E,MAAM,iCAAiC,GAAG,IAAI,GAAG,EAAE,CAAC;AAEpD;;;;;;;GAOG;AACH,SAAgB,eAAe,CAAC,cAA+C;IAC7E,OAAO,IAAA,gCAAW,EAChB;QACE;YACE,IAAI,EAAE,iBAAiB;YACvB,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,EAAE,EAAE;gBAClC,IAAI,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,MAAK,GAAG,EAAE,CAAC;oBAC7B,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;gBAChC,CAAC;gBAED,OAAO,IAAA,+BAAmB,EAAC,UAAU,EAAE;oBACrC,cAAc,EAAE,cAAc,CAAC,cAAc;oBAC7C,iBAAiB,EAAE,iCAAiC;iBACrD,CAAC,CAAC;YACL,CAAC;SACF;KACF,EACD;QACE,UAAU,EAAE,cAAc,CAAC,UAAU;KACtC,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"@azure/core-rest-pipeline\";\nimport { retryPolicy } from \"@azure/core-rest-pipeline\";\n\nimport type { MSIConfiguration } from \"./models.js\";\nimport { calculateRetryDelay } from \"@azure/core-util\";\n\n// Matches the default retry configuration in expontentialRetryStrategy.ts\nconst DEFAULT_CLIENT_MAX_RETRY_INTERVAL = 1000 * 64;\n\n/**\n * An additional policy that retries on 404 errors. The default retry policy does not retry on\n * 404s, but the IMDS endpoint can return 404s when the token is not yet available. This policy\n * will retry on 404s with an exponential backoff.\n *\n * @param msiRetryConfig - The retry configuration for the MSI credential.\n * @returns - The policy that will retry on 404s.\n */\nexport function imdsRetryPolicy(msiRetryConfig: MSIConfiguration[\"retryConfig\"]): PipelinePolicy {\n  return retryPolicy(\n    [\n      {\n        name: \"imdsRetryPolicy\",\n        retry: ({ retryCount, response }) => {\n          if (response?.status !== 404) {\n            return { skipStrategy: true };\n          }\n\n          return calculateRetryDelay(retryCount, {\n            retryDelayInMs: msiRetryConfig.startDelayInMs,\n            maxRetryDelayInMs: DEFAULT_CLIENT_MAX_RETRY_INTERVAL,\n          });\n        },\n      },\n    ],\n    {\n      maxRetries: msiRetryConfig.maxRetries,\n    },\n  );\n}\n"]}