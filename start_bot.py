#!/usr/bin/env python3
"""
Teams Bot Startup Script with Validation
Validates environment and dependencies before starting the bot
"""

import os
import sys
import subprocess
import logging
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if all required packages are installed"""
    logger.info("Checking dependencies...")
    
    required_packages = [
        'fastapi', 'uvicorn', 'azure-cognitiveservices-speech',
        'azure-communication-callautomation', 'pyaudio', 'sounddevice'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing.append(package)
    
    if missing:
        logger.error(f"Missing packages: {missing}")
        logger.info("Installing missing packages...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
            logger.info("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install dependencies: {e}")
            return False
    else:
        logger.info("✅ All dependencies are installed")
    
    return True

def validate_environment():
    """Validate environment variables"""
    logger.info("Validating environment...")
    
    load_dotenv()
    
    required_vars = ['CLIENT_ID', 'CLIENT_SECRET', 'TENANT_ID', 'SPEECH_KEY', 'SPEECH_REGION', 'USER_OBJECT_ID']
    missing = [var for var in required_vars if not os.getenv(var)]
    
    if missing:
        logger.error(f"❌ Missing required environment variables: {missing}")
        logger.error("Please check your .env file")
        return False
    
    logger.info("✅ Environment validation passed")
    return True

def start_bot():
    """Start the Teams bot server"""
    logger.info("Starting Teams Meeting Bot...")
    
    try:
        # Start the bot with uvicorn
        subprocess.run([
            sys.executable, '-m', 'uvicorn', 
            'teamsbotcau:app',
            '--host', '0.0.0.0',
            '--port', '8000',
            '--reload'
        ])
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Failed to start bot: {e}")
        return False
    
    return True

def main():
    """Main startup function"""
    logger.info("=" * 60)
    logger.info("Teams Meeting Bot - Startup Validation")
    logger.info("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        logger.error("❌ Dependency check failed")
        sys.exit(1)
    
    # Validate environment
    if not validate_environment():
        logger.error("❌ Environment validation failed")
        sys.exit(1)
    
    # Start bot
    logger.info("✅ All checks passed. Starting bot...")
    logger.info("=" * 60)
    
    if not start_bot():
        sys.exit(1)

if __name__ == "__main__":
    main()
