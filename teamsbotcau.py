import os
import asyncio
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import uuid
import threading
import re
import time
import wave
import pyaudio
import aiohttp
import sounddevice as sd
import numpy as np
import queue
import subprocess
import sys

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
import uvicorn
from pydantic import BaseModel
import azure.cognitiveservices.speech as speechsdk
from azure.communication.callautomation import CallAutomationClient
from azure.communication.identity import CommunicationIdentityClient
try:
    from azure.communication.callautomation.models import (
        StartRecordingInput,
        RecordingContentType,
        RecordingChannelType,
        RecordingFormatType,
        MicrosoftTeamsUserIdentifier,
        CallInvite
    )
except ImportError:
    # Fallback for older SDK versions
    from azure.communication.callautomation import (
        MicrosoftTeamsUserIdentifier,
        CallInvite
    )
    # Define fallback constants
    class RecordingContentType:
        AUDIO = "audio"
        AUDIO_VIDEO = "audioVideo"

    class RecordingChannelType:
        MIXED = "mixed"
        UNMIXED = "unmixed"

    class RecordingFormatType:
        WAV = "wav"
        MP4 = "mp4"
import aiofiles
from concurrent.futures import ThreadPoolExecutor
import librosa
import soundfile as sf
import msal

from dotenv import load_dotenv

load_dotenv()

app = FastAPI(title="Teams Meeting Bot with Modern Azure SDKs", version="2.0.0")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables - Load from .env file
CLIENT_ID = os.getenv("CLIENT_ID")
CLIENT_SECRET = os.getenv("CLIENT_SECRET")
TENANT_ID = os.getenv("TENANT_ID")
SPEECH_KEY = os.getenv("SPEECH_KEY")
SPEECH_REGION = os.getenv("SPEECH_REGION")
ACS_CONNECTION_STRING = os.getenv("ACS_CONNECTION_STRING", "")
USER_OBJECT_ID = os.getenv("USER_OBJECT_ID")
BOT_DOMAIN = os.getenv("BOT_DOMAIN", "https://6179-20-169-212-255.ngrok-free.app")

# Validate environment variables
def validate_environment():
    """Validate required environment variables"""
    missing_vars = []
    warnings = []

    if not CLIENT_ID:
        missing_vars.append("CLIENT_ID")
    if not CLIENT_SECRET:
        missing_vars.append("CLIENT_SECRET")
    if not TENANT_ID:
        missing_vars.append("TENANT_ID")
    if not SPEECH_KEY:
        missing_vars.append("SPEECH_KEY")
    if not SPEECH_REGION:
        missing_vars.append("SPEECH_REGION")
    if not USER_OBJECT_ID:
        missing_vars.append("USER_OBJECT_ID")

    if not ACS_CONNECTION_STRING:
        warnings.append("ACS_CONNECTION_STRING not found. Call automation features will be limited.")

    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please check your .env file and ensure all required variables are set.")
        return False

    for warning in warnings:
        logger.warning(warning)

    logger.info(f"Environment validation passed: CLIENT_ID={len(CLIENT_ID)} chars, TENANT_ID={TENANT_ID}, SPEECH_KEY={len(SPEECH_KEY)} chars, SPEECH_REGION={SPEECH_REGION}, ACS_CONNECTION_STRING={len(ACS_CONNECTION_STRING)} chars, BOT_DOMAIN={BOT_DOMAIN}")
    return True

# Validate environment on startup
if not validate_environment():
    logger.error("Environment validation failed. Please fix the issues above before running the bot.")
    sys.exit(1)

# Microsoft Graph API settings
GRAPH_API_ENDPOINT = "https://graph.microsoft.com/v1.0"
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPES = ["https://graph.microsoft.com/.default"]

class MeetingRequest(BaseModel):
    meeting_url: str
    meeting_id: Optional[str] = None
    display_name: Optional[str] = "Transcription Bot"
    enable_recording: Optional[bool] = True

class GraphAPIClient:
    def __init__(self):
        self.app = msal.ConfidentialClientApplication(
            CLIENT_ID,
            authority=AUTHORITY,
            client_credential=CLIENT_SECRET,
        )
        self.access_token = None
        self.token_expires_at = 0

    async def get_access_token(self):
        """Get Microsoft Graph API access token"""
        logger.info("Attempting to acquire Graph API token")
        if self.access_token and time.time() < self.token_expires_at:
            logger.info("Using cached token")
            return self.access_token

        try:
            result = self.app.acquire_token_silent(SCOPES, account=None)
            if not result:
                logger.info("No silent token, acquiring new token")
                result = self.app.acquire_token_for_client(scopes=SCOPES)

            if "access_token" in result:
                self.access_token = result["access_token"]
                self.token_expires_at = time.time() + result.get("expires_in", 3600) - 300
                logger.info("Token acquired successfully")
                return self.access_token
            else:
                logger.error(f"Token acquisition failed: {result}")
                raise Exception(f"Failed to get access token: {result.get('error_description')}")
        except Exception as e:
            logger.error(f"Token acquisition error: {e}")
            raise

    async def get_meeting_info(self, meeting_url: str):
        """Extract meeting information from Teams meeting URL"""
        try:
            token = await self.get_access_token()
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }

            # Extract meeting ID from URL
            meeting_id = self.extract_meeting_id_from_url(meeting_url)
            logger.info(f"Extracted meeting ID: {meeting_id} from URL: {meeting_url}")

            async with aiohttp.ClientSession() as session:
                # Use /onlineMeetings instead of /me/onlineMeetings
                url = f"{GRAPH_API_ENDPOINT}/onlineMeetings/{meeting_id}"
                logger.info(f"Sending Graph API request to: {url}")
                async with session.get(url, headers=headers) as response:
                    response_text = await response.text()
                    logger.info(f"Graph API response status: {response.status}, body: {response_text}")
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"Could not fetch meeting details: {response.status}")
                        return {"id": meeting_id, "joinUrl": meeting_url}

        except Exception as e:
            logger.error(f"Error getting meeting info: {e}")
            return {"id": str(uuid.uuid4()), "joinUrl": meeting_url}

    def extract_meeting_id_from_url(self, meeting_url: str) -> str:
        """Extract meeting ID from Teams meeting URL"""
        patterns = [
            r'meetup-join/([^/?]+)',
            r'meeting/([^/?]+)',
            r'join/([^/?]+)',
            r'[?&]meetingID=([^&]+)',
            r'[?&]threadId=([^&]+)',
            r'/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})',
        ]

        for pattern in patterns:
            match = re.search(pattern, meeting_url, re.IGNORECASE)
            if match:
                return match.group(1)

        return str(uuid.uuid4())

class AudioStreamRecorder:
    def __init__(self, filename: str, sample_rate: int = 16000, channels: int = 1):
        self.filename = filename
        self.sample_rate = sample_rate
        self.channels = channels
        self.frames = []
        self.is_recording = False
        self.audio_lock = threading.Lock()
        self.audio_queue = queue.Queue()
        self.recording_thread = None
        self.pyaudio_instance = None

    def start_recording(self):
        self.is_recording = True
        self.frames = []
        logger.info(f"Started recording audio to {self.filename}")

        # Start system audio capture in a separate thread
        self.recording_thread = threading.Thread(target=self._capture_system_audio)
        self.recording_thread.daemon = True
        self.recording_thread.start()

    def _capture_system_audio(self):
        """Capture system audio using multiple methods"""
        try:
            # Method 1: Try to capture using sounddevice (system audio)
            self._capture_with_sounddevice()
        except Exception as e:
            logger.warning(f"Sounddevice capture failed: {e}")
            try:
                # Method 2: Try to capture using PyAudio (microphone)
                self._capture_with_pyaudio()
            except Exception as e2:
                logger.error(f"PyAudio capture also failed: {e2}")
                # Method 3: Fallback to silent recording
                self._capture_silent()

    def _capture_with_sounddevice(self):
        """Capture system audio using sounddevice"""
        try:
            import sounddevice as sd

            def audio_callback(indata, frames, time, status):
                if status:
                    logger.warning(f"Audio callback status: {status}")
                if self.is_recording:
                    # Convert float32 to int16
                    audio_data = (indata * 32767).astype(np.int16)
                    self.add_audio_data(audio_data.tobytes())

            # Try to find a loopback device (Windows WASAPI)
            devices = sd.query_devices()
            loopback_device = None

            for i, device in enumerate(devices):
                if 'loopback' in device['name'].lower() or 'stereo mix' in device['name'].lower():
                    loopback_device = i
                    break

            if loopback_device is not None:
                logger.info(f"Using loopback device: {devices[loopback_device]['name']}")
                with sd.InputStream(
                    device=loopback_device,
                    channels=self.channels,
                    samplerate=self.sample_rate,
                    callback=audio_callback,
                    dtype=np.float32
                ):
                    while self.is_recording:
                        time.sleep(0.1)
            else:
                # Use default input device
                logger.info("Using default input device")
                with sd.InputStream(
                    channels=self.channels,
                    samplerate=self.sample_rate,
                    callback=audio_callback,
                    dtype=np.float32
                ):
                    while self.is_recording:
                        time.sleep(0.1)

        except Exception as e:
            logger.error(f"Sounddevice capture error: {e}")
            raise

    def _capture_with_pyaudio(self):
        """Capture audio using PyAudio"""
        try:
            self.pyaudio_instance = pyaudio.PyAudio()

            # Try to find WASAPI loopback device
            device_index = None
            for i in range(self.pyaudio_instance.get_device_count()):
                device_info = self.pyaudio_instance.get_device_info_by_index(i)
                if 'loopback' in device_info['name'].lower() or 'stereo mix' in device_info['name'].lower():
                    device_index = i
                    logger.info(f"Found loopback device: {device_info['name']}")
                    break

            stream = self.pyaudio_instance.open(
                format=pyaudio.paInt16,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=1024
            )

            logger.info("Started PyAudio recording")
            while self.is_recording:
                try:
                    data = stream.read(1024, exception_on_overflow=False)
                    self.add_audio_data(data)
                except Exception as e:
                    logger.warning(f"Audio read error: {e}")
                    break

            stream.stop_stream()
            stream.close()

        except Exception as e:
            logger.error(f"PyAudio capture error: {e}")
            raise
        finally:
            if self.pyaudio_instance:
                self.pyaudio_instance.terminate()

    def _capture_silent(self):
        """Fallback: Generate silent audio"""
        logger.warning("Using silent audio capture as fallback")
        while self.is_recording:
            # Generate 1 second of silence
            silence = b'\x00' * (self.sample_rate * 2 * self.channels)
            self.add_audio_data(silence)
            time.sleep(1.0)

    def add_audio_data(self, audio_data: bytes):
        if self.is_recording:
            with self.audio_lock:
                self.frames.append(audio_data)

    def stop_recording(self):
        self.is_recording = False
        if self.recording_thread and self.recording_thread.is_alive():
            self.recording_thread.join(timeout=5.0)
        self.save_audio()

    def save_audio(self):
        if not self.frames:
            logger.warning("No audio frames to save")
            return

        try:
            with wave.open(self.filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(2)  # 16-bit audio
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(self.frames))
            logger.info(f"Audio saved to {self.filename}")
        except Exception as e:
            logger.error(f"Error saving audio: {e}")

class ModernTranscriptionSession:
    def __init__(self, meeting_id: str, meeting_info: Dict[str, Any], display_name: str = "Bot"):
        self.meeting_id = meeting_id
        self.meeting_info = meeting_info
        self.display_name = display_name
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.transcription_file = f"transcription_{meeting_id}_{timestamp}.txt"
        self.audio_file = f"audio_{meeting_id}_{timestamp}.wav"
        self.raw_audio_file = f"raw_audio_{meeting_id}_{timestamp}.wav"
        self.recording_file = f"recording_{meeting_id}_{timestamp}.wav"
        self.is_active = False
        self.speech_recognizer = None
        self.transcript_data = []
        self.call_automation_client = None
        self.call_connection = None
        self.executor = ThreadPoolExecutor(max_workers=3)
        self.audio_recorder = AudioStreamRecorder(self.raw_audio_file)
        self.graph_client = GraphAPIClient()
        self.recording_id = None
        self.pyaudio_instance = None

    async def start_transcription(self):
        """Start the transcription session with modern Azure SDKs"""
        try:
            self.pyaudio_instance = pyaudio.PyAudio()
            # Initialize Speech SDK for transcription
            await self._setup_speech_recognition()

            # Initialize Call Automation if ACS is configured
            if ACS_CONNECTION_STRING:
                await self._setup_call_automation()

            self.is_active = True
            self.audio_recorder.start_recording()

            # Start continuous speech recognition
            if self.speech_recognizer:
                self.speech_recognizer.start_continuous_recognition_async()
                logger.info("Started continuous speech recognition")

            logger.info(f"Successfully started modern transcription session for meeting {self.meeting_id}")

        except Exception as e:
            logger.error(f"Error starting transcription: {e}")
            raise

    async def _setup_speech_recognition(self):
        """Setup Azure Speech Services for transcription"""
        try:
            logger.info(f"Setting up speech recognition with key: {len(SPEECH_KEY) if SPEECH_KEY else 0} chars, region: {SPEECH_REGION}")
            if not SPEECH_KEY or not SPEECH_REGION:
                raise ValueError("SPEECH_KEY or SPEECH_REGION is missing")

            speech_config = speechsdk.SpeechConfig(subscription=SPEECH_KEY, region=SPEECH_REGION)
            speech_config.speech_recognition_language = "en-US"
            speech_config.enable_dictation()
            speech_config.set_property(speechsdk.PropertyId.SpeechServiceConnection_RecoMode, "CONVERSATION")
            speech_config.set_property(speechsdk.PropertyId.Speech_SegmentationSilenceTimeoutMs, "2000")

            # Attempt to set continuous recognition timeout
            try:
                speech_config.set_property(
                    speechsdk.PropertyId.SpeechServiceConnection_ContinuousRecognitionTimeoutMs,
                    "300000"
                )
                logger.info("Set ContinuousRecognitionTimeoutMs successfully")
            except Exception as e:
                logger.warning(f"Failed to set ContinuousRecognitionTimeoutMs: {e}, proceeding without it")

            # Use push audio input stream for real-time processing
            self.audio_stream = speechsdk.audio.PushAudioInputStream()
            audio_config = speechsdk.audio.AudioConfig(stream=self.audio_stream)

            self.speech_recognizer = speechsdk.SpeechRecognizer(speech_config=speech_config, audio_config=audio_config)

            # Setup event handlers
            self.speech_recognizer.recognized.connect(self._on_speech_recognized)
            self.speech_recognizer.recognizing.connect(self._on_speech_recognizing)
            self.speech_recognizer.session_stopped.connect(self._on_session_stopped)
            self.speech_recognizer.canceled.connect(self._on_recognition_canceled)

            logger.info("Speech recognition setup completed")

        except Exception as e:
            logger.error(f"Error setting up speech recognition: {e}")
            raise

    async def _setup_call_automation(self):
        """Setup Call Automation client for advanced call management"""
        try:
            if not ACS_CONNECTION_STRING:
                logger.warning("ACS not configured, skipping call automation setup")
                return

            self.call_automation_client = CallAutomationClient.from_connection_string(ACS_CONNECTION_STRING)
            logger.info("Call Automation client initialized")

            # Setup call connection to Teams meeting
            meeting_url = self.meeting_info.get('joinUrl', '')
            if meeting_url:
                # Create a call invite to the Teams meeting
                target = MicrosoftTeamsUserIdentifier(user_id=USER_OBJECT_ID)
                call_invite = CallInvite(target=target)

                # Join the meeting
                call_connection = self.call_automation_client.create_call(
                    target_participant=target,
                    callback_url=f"{BOT_DOMAIN}/api/recording-status",
                    source_display_name=self.display_name
                )
                self.call_connection = call_connection
                logger.info(f"Joined Teams meeting {self.meeting_id} via Call Automation")

                # Setup audio stream handling
                self._setup_audio_handlers()

        except Exception as e:
            logger.error(f"Error setting up call automation: {e}")

    def _setup_audio_handlers(self):
        """Setup audio stream handlers for the call"""
        try:
            if self.call_connection:
                # media = self.call_connection.get_media()
                # media.subscribe_to_audio_stream(self.process_audio_stream)
                logger.info(f"Call connection established with ID: {self.call_connection.call_connection_id}")
                logger.info("Audio stream handlers setup completed")
        except Exception as e:
            logger.error(f"Error setting up audio handlers: {e}")

    def _on_speech_recognized(self, evt):
        """Handle recognized speech"""
        try:
            if evt.result.reason == speechsdk.ResultReason.RecognizedSpeech and evt.result.text.strip():
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                transcript_entry = {
                    "timestamp": timestamp,
                    "speaker": getattr(evt.result, 'speaker_id', 'Unknown'),
                    "text": evt.result.text.strip(),
                    "confidence": evt.result.properties.get(speechsdk.PropertyId.SpeechServiceResponse_JsonResult, ""),
                    "session_id": evt.session_id if hasattr(evt, 'session_id') else None,
                    "result_id": evt.result.result_id if hasattr(evt.result, 'result_id') else None
                }

                self.transcript_data.append(transcript_entry)
                # Use thread-safe approach for async operations
                self.executor.submit(self._save_transcript_sync, transcript_entry)
                logger.info(f"Transcribed: {evt.result.text.strip()[:50]}...")
        except Exception as e:
            logger.error(f"Error in speech recognized callback: {e}")

    def _save_transcript_sync(self, entry: Dict[str, Any]):
        """Thread-safe wrapper for saving transcript"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.save_transcript_chunk(entry))
            loop.close()
        except Exception as e:
            logger.error(f"Error in sync transcript save: {e}")

    def _on_speech_recognizing(self, evt):
        """Handle intermediate recognition results"""
        try:
            if evt.result.reason == speechsdk.ResultReason.RecognizingSpeech and evt.result.text.strip():
                logger.debug(f"Recognizing: {evt.result.text}")
        except Exception as e:
            logger.error(f"Error in speech recognizing callback: {e}")

    def _on_session_stopped(self, evt):
        """Handle session stopped event"""
        try:
            logger.info("Speech recognition session stopped")
            self.is_active = False
            # Use thread-safe approach
            self.executor.submit(self._finalize_transcription_sync)
        except Exception as e:
            logger.error(f"Error in session stopped callback: {e}")

    def _finalize_transcription_sync(self):
        """Thread-safe wrapper for finalizing transcription"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.finalize_transcription())
            loop.close()
        except Exception as e:
            logger.error(f"Error in sync transcription finalization: {e}")

    def _on_recognition_canceled(self, evt):
        """Handle recognition canceled event"""
        try:
            logger.error(f"Speech recognition canceled: {evt.cancellation_details}")
            self.is_active = False
            # Use thread-safe approach
            self.executor.submit(self._finalize_transcription_sync)
        except Exception as e:
            logger.error(f"Error in recognition canceled callback: {e}")

    async def save_transcript_chunk(self, entry: Dict[str, Any]):
        """Save transcript entry to file"""
        try:
            async with aiofiles.open(self.transcription_file, 'a', encoding='utf-8') as f:
                speaker_info = f"[{entry.get('speaker', 'Unknown')}] " if entry.get('speaker') != 'Unknown' else ""
                await f.write(f"[{entry['timestamp']}] {speaker_info}{entry['text']}\n")
        except Exception as e:
            logger.error(f"Error saving transcript chunk: {e}")

    def process_audio_stream(self, audio_data: bytes):
        """Process incoming audio stream data"""
        try:
            # Save raw audio data
            self.audio_recorder.add_audio_data(audio_data)

            # Push audio to speech recognizer for real-time transcription
            if hasattr(self, 'audio_stream') and self.audio_stream:
                self.audio_stream.write(audio_data)

        except Exception as e:
            logger.error(f"Error processing audio stream: {e}")

    async def start_recording(self):
        """Start call recording using Call Automation"""
        try:
            if not self.call_automation_client or not self.call_connection:
                logger.warning("Call Automation not available for recording")
                return

            # Start recording with proper API call
            try:
                # Use the modern API if available
                if hasattr(self.call_automation_client, 'start_recording'):
                    recording_input = {
                        "recordingContent": RecordingContentType.AUDIO,
                        "recordingChannel": RecordingChannelType.MIXED,
                        "recordingFormat": RecordingFormatType.WAV,
                        "recordingStateCallbackUri": f"{BOT_DOMAIN}/api/recording-status"
                    }

                    result = self.call_automation_client.start_recording(
                        call_locator={"callConnectionId": self.call_connection.call_connection_id},
                        **recording_input
                    )
                else:
                    # Fallback for older API
                    logger.warning("Using fallback recording method")
                    result = None
            except Exception as api_error:
                logger.error(f"Recording API error: {api_error}")
                result = None
            if result:
                self.recording_id = result.recording_id
                logger.info(f"Started recording with ID: {self.recording_id}")

        except Exception as e:
            logger.error(f"Error starting recording: {e}")

    async def stop_recording(self):
        """Stop call recording"""
        try:
            if self.call_automation_client and self.recording_id:
                self.call_automation_client.stop_recording(recording_id=self.recording_id)
                logger.info(f"Stopped recording {self.recording_id}")

        except Exception as e:
            logger.error(f"Error stopping recording: {e}")

    async def get_meeting_transcript_from_graph(self):
        """Retrieve meeting transcript from Microsoft Graph API"""
        try:
            token = await self.graph_client.get_access_token()
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                url = f"{GRAPH_API_ENDPOINT}/me/onlineMeetings/{self.meeting_id}/transcripts"
                logger.info(f"Fetching transcript from: {url}")
                async with session.get(url, headers=headers) as response:
                    response_text = await response.text()
                    logger.info(f"Transcript response status: {response.status}, body: {response_text}")
                    if response.status == 200:
                        transcripts = await response.json()
                        return transcripts.get('value', [])
                    else:
                        logger.warning(f"Could not fetch transcripts: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"Error getting transcript from Graph API: {e}")
            return None

    async def save_processed_audio(self):
        """Save and process the recorded audio"""
        try:
            if os.path.exists(self.raw_audio_file):
                audio_data, sample_rate = librosa.load(self.raw_audio_file, sr=16000)
                audio_data = librosa.util.normalize(audio_data)
                sf.write(self.audio_file, audio_data, sample_rate)
                logger.info(f"Processed audio saved to {self.audio_file}")

        except Exception as e:
            logger.error(f"Error processing saved audio: {e}")

    async def finalize_transcription(self):
        """Finalize the transcription session"""
        try:
            self.audio_recorder.stop_recording()
            await self.save_processed_audio()

            graph_transcript = await self.get_meeting_transcript_from_graph()

            summary = {
                "meeting_id": self.meeting_id,
                "meeting_info": self.meeting_info,
                "display_name": self.display_name,
                "total_entries": len(self.transcript_data),
                "total_speakers": len(set(entry.get('speaker', 'Unknown') for entry in self.transcript_data)),
                "start_time": self.transcript_data[0]["timestamp"] if self.transcript_data else None,
                "end_time": self.transcript_data[-1]["timestamp"] if self.transcript_data else None,
                "transcription_file": self.transcription_file,
                "audio_file": self.audio_file,
                "raw_audio_file": self.raw_audio_file,
                "recording_file": self.recording_file,
                "recording_id": self.recording_id,
                "speakers": list(set(entry.get('speaker', 'Unknown') for entry in self.transcript_data)),
                "our_transcript": [{"timestamp": entry["timestamp"], "speaker": entry.get("speaker", "Unknown"), "text": entry["text"]} for entry in self.transcript_data],
                "graph_transcript": graph_transcript,
                "session_duration": self._calculate_session_duration()
            }

            summary_file = f"summary_{self.meeting_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            async with aiofiles.open(summary_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(summary, indent=2))

            logger.info(f"Transcription finalized: {summary_file}")
            return summary

        except Exception as e:
            logger.error(f"Error finalizing transcription: {e}")
            return None

    def _calculate_session_duration(self):
        """Calculate session duration"""
        if len(self.transcript_data) >= 2:
            try:
                start_time = datetime.strptime(self.transcript_data[0]["timestamp"], "%Y-%m-%d %H:%M:%S")
                end_time = datetime.strptime(self.transcript_data[-1]["timestamp"], "%Y-%m-%d %H:%M:%S")
                return str(end_time - start_time)
            except:
                pass
        return "Unknown"

    def stop_transcription(self):
        """Stop the transcription session"""
        try:
            if self.speech_recognizer and self.is_active:
                if hasattr(self, 'audio_stream') and self.audio_stream:
                    self.audio_stream.close()
                self.speech_recognizer.stop_continuous_recognition_async()
                self.is_active = False
                logger.info("Stopped speech recognition")

            if self.pyaudio_instance:
                self.pyaudio_instance.terminate()

        except Exception as e:
            logger.error(f"Error stopping transcription: {e}")

class ModernTeamsBot:
    def __init__(self):
        self.active_sessions: Dict[str, ModernTranscriptionSession] = {}
        self.graph_client = GraphAPIClient()

        if ACS_CONNECTION_STRING:
            try:
                self.identity_client = CommunicationIdentityClient.from_connection_string(ACS_CONNECTION_STRING)
                self.call_automation_client = CallAutomationClient.from_connection_string(ACS_CONNECTION_STRING)
                logger.info("Azure Communication Services initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing ACS: {e}")
                self.identity_client = None
                self.call_automation_client = None
        else:
            self.identity_client = None
            self.call_automation_client = None

    async def join_meeting_with_transcription(self, meeting_url: str, meeting_id: Optional[str] = None, display_name: str = "Transcription Bot", enable_recording: bool = True) -> str:
        """Join meeting and start transcription using modern approach"""
        try:
            meeting_info = await self.graph_client.get_meeting_info(meeting_url)

            if not meeting_id:
                meeting_id = meeting_info.get('id', str(uuid.uuid4()))

            transcription_session = ModernTranscriptionSession(meeting_id, meeting_info, display_name)
            self.active_sessions[meeting_id] = transcription_session

            await transcription_session.start_transcription()

            if enable_recording and self.call_automation_client:
                await transcription_session.start_recording()

            logger.info(f"Successfully started modern session for meeting {meeting_id}")
            return meeting_id

        except Exception as e:
            logger.error(f"Error joining meeting: {e}")
            if meeting_id in self.active_sessions:
                del self.active_sessions[meeting_id]
            raise HTTPException(status_code=500, detail=f"Failed to join meeting: {str(e)}")

    async def leave_meeting(self, meeting_id: str):
        """Leave meeting and finalize transcription"""
        if meeting_id not in self.active_sessions:
            raise HTTPException(status_code=404, detail="Meeting session not found")

        session = self.active_sessions[meeting_id]

        try:
            await session.stop_recording()
            session.stop_transcription()
            summary = await session.finalize_transcription()
            del self.active_sessions[meeting_id]

            logger.info(f"Successfully left meeting {meeting_id}")
            return summary

        except Exception as e:
            logger.error(f"Error leaving meeting: {e}")
            raise HTTPException(status_code=500, detail=f"Error leaving meeting: {str(e)}")

    def get_session_status(self, meeting_id: str) -> Dict[str, Any]:
        """Get detailed session status"""
        if meeting_id not in self.active_sessions:
            return {"status": "not_found", "message": "Meeting session not found"}

        session = self.active_sessions[meeting_id]

        return {
            "status": "active" if session.is_active else "inactive",
            "meeting_id": meeting_id,
            "meeting_info": session.meeting_info,
            "display_name": session.display_name,
            "transcription_file": session.transcription_file,
            "audio_file": session.audio_file,
            "raw_audio_file": session.raw_audio_file,
            "recording_file": session.recording_file,
            "recording_id": session.recording_id,
            "transcript_entries": len(session.transcript_data),
            "audio_files": {
                "transcription": session.transcription_file,
                "processed_audio": session.audio_file,
                "raw_audio": session.raw_audio_file,
                "recording": session.recording_file
            },
            "latest_transcript": session.transcript_data[-5:] if session.transcript_data else [],
            "speakers_detected": len(set(entry.get('speaker', 'Unknown') for entry in self.transcript_data)),
            "session_duration": session._calculate_session_duration(),
            "acs_enabled": bool(self.call_automation_client),
            "graph_api_enabled": bool(CLIENT_ID and CLIENT_SECRET and TENANT_ID)
        }

    def get_meeting_analytics(self, meeting_id: str) -> Dict[str, Any]:
        """Get detailed meeting analytics"""
        if meeting_id not in self.active_sessions:
            return {"status": "not_found", "message": "Meeting session not found"}

        session = self.active_sessions[meeting_id]

        total_words = sum(len(entry['text'].split()) for entry in session.transcript_data)
        speakers = list(set(entry.get('speaker', 'Unknown') for entry in session.transcript_data))

        speaker_stats = {}
        for speaker in speakers:
            speaker_entries = [entry for entry in session.transcript_data if entry.get('speaker') == speaker]
            speaker_stats[speaker] = {
                "total_messages": len(speaker_entries),
                "total_words": sum(len(entry['text'].split()) for entry in speaker_entries),
                "first_message": speaker_entries[0]['timestamp'] if speaker_entries else None,
                "last_message": speaker_entries[-1]['timestamp'] if speaker_entries else None
            }

        analytics = {
            "meeting_id": meeting_id,
            "total_transcript_entries": len(session.transcript_data),
            "total_words_spoken": total_words,
            "unique_speakers": len(speakers),
            "meeting_duration": session._calculate_session_duration(),
            "speaker_statistics": speaker_stats,
            "audio_files": {
                "raw_audio_exists": os.path.exists(session.raw_audio_file),
                "processed_audio_exists": os.path.exists(session.audio_file),
                "recording_exists": os.path.exists(session.recording_file) if session.recording_file else False,
                "raw_audio_size": os.path.getsize(session.raw_audio_file) if os.path.exists(session.raw_audio_file) else 0,
                "processed_audio_size": os.path.getsize(session.audio_file) if os.path.exists(session.audio_file) else 0,
                "recording_size": os.path.getsize(session.recording_file) if session.recording_file and os.path.exists(session.recording_file) else 0
            }
        }

        return analytics

bot = ModernTeamsBot()

@app.post("/join-meeting")
async def join_meeting(request: MeetingRequest, background_tasks: BackgroundTasks):
    """Join a Teams meeting and start transcription"""
    try:
        meeting_id = await bot.join_meeting_with_transcription(
            request.meeting_url,
            request.meeting_id,
            request.display_name or "Transcription Bot",
            request.enable_recording or True
        )
        return JSONResponse(
            content={
                "status": "success",
                "meeting_id": meeting_id,
                "message": "Successfully joined meeting and started transcription",
                "transcription_file": f"transcription_{meeting_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "modern_features": {
                    "graph_api_integration": True,
                    "call_automation": bool(ACS_CONNECTION_STRING),
                    "enhanced_transcription": True
                }
            },
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error joining meeting: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/leave-meeting/{meeting_id}")
async def leave_meeting(meeting_id: str):
    """Leave meeting and save all data"""
    try:
        summary = await bot.leave_meeting(meeting_id)
        return JSONResponse(
            content={
                "status": "success",
                "meeting_id": meeting_id,
                "message": "Successfully left meeting and saved transcription",
                "summary": summary
            },
            status_code=200
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error leaving meeting: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/meeting-status/{meeting_id}")
async def get_meeting_status(meeting_id: str):
    """Get comprehensive meeting status"""
    try:
        status = bot.get_session_status(meeting_id)
        return JSONResponse(content=status, status_code=200)
    except Exception as e:
        logger.error(f"Error getting meeting status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/active-meetings")
async def get_active_meetings():
    """Get all active meeting sessions"""
    try:
        active_meetings = []
        for meeting_id, session in bot.active_sessions.items():
            active_meetings.append({
                "meeting_id": meeting_id,
                "display_name": session.display_name,
                "is_active": session.is_active,
                "meeting_info": session.meeting_info,
                "transcription_file": session.transcription_file,
                "transcript_entries": len(session.transcript_data),
                "last_activity": session.transcript_data[-1]["timestamp"] if session.transcript_data else None,
                "recording_id": session.recording_id,
                "session_duration": session._calculate_session_duration()
            })

        return JSONResponse(
            content={
                "active_meetings": active_meetings,
                "total_count": len(active_meetings),
                "modern_features_enabled": {
                    "graph_api": bool(CLIENT_ID and CLIENT_SECRET and TENANT_ID),
                    "call_automation": bool(ACS_CONNECTION_STRING),
                    "speech_services": bool(SPEECH_KEY and SPEECH_REGION)
                }
            },
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error getting active meetings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/download-transcript/{meeting_id}")
async def download_transcript(meeting_id: str):
    """Download meeting transcript"""
    try:
        if meeting_id not in bot.active_sessions:
            raise HTTPException(status_code=404, detail="Meeting session not found")

        session = bot.active_sessions[meeting_id]

        if not os.path.exists(session.transcription_file):
            raise HTTPException(status_code=404, detail="Transcription file not found")

        return FileResponse(
            path=session.transcription_file,
            filename=os.path.basename(session.transcription_file),
            media_type="text/plain"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading transcript: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/download-audio/{meeting_id}")
async def download_audio(meeting_id: str, audio_type: str = "processed"):
    """Download meeting audio"""
    try:
        if meeting_id not in bot.active_sessions:
            raise HTTPException(status_code=404, detail="Meeting session not found")

        session = bot.active_sessions[meeting_id]

        audio_file = session.raw_audio_file if audio_type == "raw" else session.audio_file

        if not os.path.exists(audio_file):
            raise HTTPException(status_code=404, detail=f"Audio file not found: {audio_file}")

        return FileResponse(
            path=audio_file,
            filename=os.path.basename(audio_file),
            media_type="audio/wav"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading audio: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/meeting-analytics/{meeting_id}")
async def get_meeting_analytics(meeting_id: str):
    """Get detailed meeting analytics"""
    try:
        analytics = bot.get_meeting_analytics(meeting_id)
        return JSONResponse(content=analytics, status_code=200)
    except Exception as e:
        logger.error(f"Error getting meeting analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return JSONResponse(
        content={
            "status": "healthy",
            "acs_configured": bool(ACS_CONNECTION_STRING),
            "speech_configured": bool(SPEECH_KEY and SPEECH_REGION),
            "graph_api_configured": bool(CLIENT_ID and CLIENT_SECRET and TENANT_ID),
            "active_sessions": len(bot.active_sessions)
        },
        status_code=200
    )

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return JSONResponse(
        content={
            "message": "Teams Meeting Bot API with Live Transcription",
            "version": "2.0.0",
            "features": [
                "Join Teams meetings via URL",
                "Live speech-to-text transcription",
                "Meeting recording via Azure Communication Services",
                "Microsoft Graph API integration for meeting details",
                "Audio processing and normalization",
                "Session analytics and reporting",
                "Local file storage for transcripts and audio"
            ],
            "endpoints": [
                "POST /join-meeting - Join a meeting and start transcription",
                "POST /leave-meeting/{meeting_id} - Leave meeting and save transcription",
                "GET /meeting-status/{meeting_id} - Get meeting status",
                "GET /active-meetings - List all active meetings",
                "GET /download-transcript/{meeting_id} - Download transcription file",
                "GET /download-audio/{meeting_id}?audio_type=processed|raw - Download audio file",
                "GET /meeting-analytics/{meeting_id} - Get detailed meeting analytics",
                "GET /health - Health check",
                "GET / - API information"
            ],
            "requirements": [
                "ACS_CONNECTION_STRING for joining meetings and recording",
                "SPEECH_KEY and SPEECH_REGION for transcription",
                "CLIENT_ID, CLIENT_SECRET, TENANT_ID for Graph API integration",
                "BOT_DOMAIN for ACS callback handling"
            ]
        },
        status_code=200
    )

@app.post("/api/recording-status")
async def recording_status(request: dict):
    """Handle ACS recording status callbacks"""
    logger.info(f"Received ACS recording status: {request}")
    return JSONResponse(content={"status": "received"}, status_code=200)

if __name__ == "__main__":
    uvicorn.run(
        "teamsbotcau:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )