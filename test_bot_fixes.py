#!/usr/bin/env python3
"""
Comprehensive test script for Teams Bot fixes
Tests all major components and configurations
"""

import os
import sys
import asyncio
import logging
import json
import time
from datetime import datetime
import requests
import subprocess
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_results.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BotTester:
    def __init__(self):
        load_dotenv()
        self.test_results = {}
        self.start_time = datetime.now()
        
    def test_environment_variables(self):
        """Test environment variable configuration"""
        logger.info("Testing environment variables...")
        
        required_vars = [
            'CLIENT_ID', 'CLIENT_SECRET', 'TENANT_ID', 
            'SPEECH_KEY', 'SPEECH_REGION', 'USER_OBJECT_ID'
        ]
        
        optional_vars = ['ACS_CONNECTION_STRING', 'BOT_DOMAIN']
        
        missing_required = []
        missing_optional = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_required.append(var)
        
        for var in optional_vars:
            if not os.getenv(var):
                missing_optional.append(var)
        
        self.test_results['environment'] = {
            'status': 'PASS' if not missing_required else 'FAIL',
            'missing_required': missing_required,
            'missing_optional': missing_optional,
            'details': f"Required vars: {len(required_vars) - len(missing_required)}/{len(required_vars)}"
        }
        
        if missing_required:
            logger.error(f"Missing required environment variables: {missing_required}")
        else:
            logger.info("✅ All required environment variables are set")
            
        if missing_optional:
            logger.warning(f"Missing optional environment variables: {missing_optional}")
    
    def test_dependencies(self):
        """Test Python package dependencies"""
        logger.info("Testing Python dependencies...")
        
        required_packages = [
            'fastapi', 'uvicorn', 'azure-cognitiveservices-speech',
            'azure-communication-callautomation', 'azure-communication-identity',
            'pyaudio', 'librosa', 'soundfile', 'aiohttp', 'aiofiles',
            'msal', 'sounddevice', 'numpy'
        ]
        
        missing_packages = []
        installed_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                installed_packages.append(package)
            except ImportError:
                missing_packages.append(package)
        
        self.test_results['dependencies'] = {
            'status': 'PASS' if not missing_packages else 'FAIL',
            'missing': missing_packages,
            'installed': installed_packages,
            'details': f"Installed: {len(installed_packages)}/{len(required_packages)}"
        }
        
        if missing_packages:
            logger.error(f"Missing packages: {missing_packages}")
            logger.info("Install missing packages with: pip install -r requirements.txt")
        else:
            logger.info("✅ All required packages are installed")
    
    def test_audio_devices(self):
        """Test audio device availability"""
        logger.info("Testing audio devices...")
        
        try:
            import sounddevice as sd
            devices = sd.query_devices()
            
            input_devices = [d for d in devices if d['max_input_channels'] > 0]
            loopback_devices = [d for d in devices if 'loopback' in d['name'].lower() or 'stereo mix' in d['name'].lower()]
            
            self.test_results['audio_devices'] = {
                'status': 'PASS' if input_devices else 'FAIL',
                'total_devices': len(devices),
                'input_devices': len(input_devices),
                'loopback_devices': len(loopback_devices),
                'details': f"Found {len(input_devices)} input devices, {len(loopback_devices)} loopback devices"
            }
            
            logger.info(f"✅ Found {len(input_devices)} input devices")
            if loopback_devices:
                logger.info(f"✅ Found {len(loopback_devices)} loopback devices for system audio capture")
            else:
                logger.warning("⚠️ No loopback devices found. System audio capture may not work.")
                
        except Exception as e:
            self.test_results['audio_devices'] = {
                'status': 'FAIL',
                'error': str(e),
                'details': 'Failed to query audio devices'
            }
            logger.error(f"❌ Audio device test failed: {e}")
    
    def test_azure_speech_service(self):
        """Test Azure Speech Service connectivity"""
        logger.info("Testing Azure Speech Service...")
        
        try:
            import azure.cognitiveservices.speech as speechsdk
            
            speech_key = os.getenv('SPEECH_KEY')
            speech_region = os.getenv('SPEECH_REGION')
            
            if not speech_key or not speech_region:
                raise ValueError("SPEECH_KEY or SPEECH_REGION not configured")
            
            speech_config = speechsdk.SpeechConfig(subscription=speech_key, region=speech_region)
            speech_config.speech_recognition_language = "en-US"
            
            # Test with a simple recognizer
            recognizer = speechsdk.SpeechRecognizer(speech_config=speech_config)
            
            self.test_results['azure_speech'] = {
                'status': 'PASS',
                'region': speech_region,
                'details': 'Speech service configuration successful'
            }
            
            logger.info("✅ Azure Speech Service configuration successful")
            
        except Exception as e:
            self.test_results['azure_speech'] = {
                'status': 'FAIL',
                'error': str(e),
                'details': 'Failed to configure Azure Speech Service'
            }
            logger.error(f"❌ Azure Speech Service test failed: {e}")
    
    def test_azure_communication_services(self):
        """Test Azure Communication Services"""
        logger.info("Testing Azure Communication Services...")
        
        try:
            from azure.communication.callautomation import CallAutomationClient
            from azure.communication.identity import CommunicationIdentityClient
            
            acs_connection_string = os.getenv('ACS_CONNECTION_STRING')
            
            if not acs_connection_string:
                self.test_results['azure_acs'] = {
                    'status': 'SKIP',
                    'details': 'ACS_CONNECTION_STRING not configured'
                }
                logger.warning("⚠️ ACS_CONNECTION_STRING not configured. Call automation features will be limited.")
                return
            
            # Test ACS clients
            identity_client = CommunicationIdentityClient.from_connection_string(acs_connection_string)
            call_automation_client = CallAutomationClient.from_connection_string(acs_connection_string)
            
            self.test_results['azure_acs'] = {
                'status': 'PASS',
                'details': 'ACS clients initialized successfully'
            }
            
            logger.info("✅ Azure Communication Services configuration successful")
            
        except Exception as e:
            self.test_results['azure_acs'] = {
                'status': 'FAIL',
                'error': str(e),
                'details': 'Failed to configure Azure Communication Services'
            }
            logger.error(f"❌ Azure Communication Services test failed: {e}")
    
    def test_microsoft_graph_api(self):
        """Test Microsoft Graph API connectivity"""
        logger.info("Testing Microsoft Graph API...")
        
        try:
            import msal
            
            client_id = os.getenv('CLIENT_ID')
            client_secret = os.getenv('CLIENT_SECRET')
            tenant_id = os.getenv('TENANT_ID')
            
            if not all([client_id, client_secret, tenant_id]):
                raise ValueError("CLIENT_ID, CLIENT_SECRET, or TENANT_ID not configured")
            
            authority = f"https://login.microsoftonline.com/{tenant_id}"
            scopes = ["https://graph.microsoft.com/.default"]
            
            app = msal.ConfidentialClientApplication(
                client_id,
                authority=authority,
                client_credential=client_secret,
            )
            
            # Test token acquisition
            result = app.acquire_token_for_client(scopes=scopes)
            
            if "access_token" in result:
                self.test_results['microsoft_graph'] = {
                    'status': 'PASS',
                    'tenant_id': tenant_id,
                    'details': 'Graph API token acquired successfully'
                }
                logger.info("✅ Microsoft Graph API authentication successful")
            else:
                raise Exception(f"Token acquisition failed: {result.get('error_description')}")
                
        except Exception as e:
            self.test_results['microsoft_graph'] = {
                'status': 'FAIL',
                'error': str(e),
                'details': 'Failed to authenticate with Microsoft Graph API'
            }
            logger.error(f"❌ Microsoft Graph API test failed: {e}")
    
    def test_bot_server(self):
        """Test if the bot server can start"""
        logger.info("Testing bot server startup...")
        
        try:
            # Import the main bot module to check for syntax errors
            import teamsbotcau
            
            self.test_results['bot_server'] = {
                'status': 'PASS',
                'details': 'Bot module imported successfully'
            }
            
            logger.info("✅ Bot server module loads successfully")
            
        except Exception as e:
            self.test_results['bot_server'] = {
                'status': 'FAIL',
                'error': str(e),
                'details': 'Failed to import bot module'
            }
            logger.error(f"❌ Bot server test failed: {e}")
    
    def run_all_tests(self):
        """Run all tests and generate report"""
        logger.info("Starting comprehensive bot testing...")
        logger.info("=" * 60)
        
        tests = [
            self.test_environment_variables,
            self.test_dependencies,
            self.test_audio_devices,
            self.test_azure_speech_service,
            self.test_azure_communication_services,
            self.test_microsoft_graph_api,
            self.test_bot_server
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                logger.error(f"Test {test.__name__} failed with exception: {e}")
            logger.info("-" * 40)
        
        self.generate_report()
    
    def generate_report(self):
        """Generate test report"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        passed = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed = sum(1 for result in self.test_results.values() if result['status'] == 'FAIL')
        skipped = sum(1 for result in self.test_results.values() if result['status'] == 'SKIP')
        total = len(self.test_results)
        
        logger.info("=" * 60)
        logger.info("TEST REPORT")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {total}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        logger.info(f"Skipped: {skipped}")
        logger.info(f"Duration: {duration.total_seconds():.2f} seconds")
        logger.info("=" * 60)
        
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'PASS' else "❌" if result['status'] == 'FAIL' else "⚠️"
            logger.info(f"{status_icon} {test_name.upper()}: {result['status']} - {result['details']}")
        
        # Save detailed report
        report = {
            'timestamp': end_time.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'summary': {
                'total': total,
                'passed': passed,
                'failed': failed,
                'skipped': skipped
            },
            'results': self.test_results
        }
        
        with open('test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info("=" * 60)
        logger.info("Detailed report saved to: test_report.json")
        logger.info("Test logs saved to: test_results.log")
        
        if failed > 0:
            logger.error(f"❌ {failed} tests failed. Please fix the issues above.")
            return False
        else:
            logger.info("✅ All tests passed! The bot should work correctly.")
            return True

if __name__ == "__main__":
    tester = BotTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
