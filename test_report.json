{"timestamp": "2025-06-17T05:56:54.233717", "duration_seconds": 4.349487, "summary": {"total": 7, "passed": 6, "failed": 1, "skipped": 0}, "results": {"environment": {"status": "PASS", "missing_required": [], "missing_optional": [], "details": "Required vars: 6/6"}, "dependencies": {"status": "FAIL", "missing": ["azure-cognitiveservices-speech", "azure-communication-callautomation", "azure-communication-identity"], "installed": ["<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "pya<PERSON>o", "librosa", "soundfile", "aiohttp", "aiofiles", "msal", "sounddevice", "numpy"], "details": "Installed: 10/13"}, "audio_devices": {"status": "PASS", "total_devices": 13, "input_devices": 7, "loopback_devices": 0, "details": "Found 7 input devices, 0 loopback devices"}, "azure_speech": {"status": "PASS", "region": "eastus", "details": "Speech service configuration successful"}, "azure_acs": {"status": "PASS", "details": "ACS clients initialized successfully"}, "microsoft_graph": {"status": "PASS", "tenant_id": "9750f5c0-d037-4115-a5b2-b25c5994ed60", "details": "Graph API token acquired successfully"}, "bot_server": {"status": "PASS", "details": "Bot module imported successfully"}}}